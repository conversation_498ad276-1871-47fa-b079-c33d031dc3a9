"""
版本管理API端点
"""

from typing import Any, List
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session

from app.core.database import get_db
from app.core.auth import get_current_user
from app.models.user import User
from app.services.version_service import VersionService
from app.schemas.version import (
    QuestionVersionCreate, QuestionVersionResponse, QuestionVersionUpdate,
    KpVersionCreate, KpVersionResponse, KpVersionUpdate,
    VersionListResponse, VersionCompareRequest, VersionCompareResponse,
    VersionRollbackRequest, VersionRollbackResponse,
    VersionPublishRequest, VersionPublishResponse,
    VersionStatsResponse
)

router = APIRouter()


@router.post("/questions", response_model=QuestionVersionResponse)
async def create_question_version(
    *,
    db: Session = Depends(get_db),
    version_in: QuestionVersionCreate,
    current_user: User = Depends(get_current_user)
) -> Any:
    """
    创建题目版本
    """
    version_service = VersionService(db)
    try:
        version = version_service.create_question_version(version_in, current_user.id)
        return version
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.post("/knowledge-points", response_model=KpVersionResponse)
async def create_kp_version(
    *,
    db: Session = Depends(get_db),
    version_in: KpVersionCreate,
    current_user: User = Depends(get_current_user)
) -> Any:
    """
    创建知识点版本
    """
    version_service = VersionService(db)
    try:
        version = version_service.create_kp_version(version_in, current_user.id)
        return version
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.get("/questions/{question_id}", response_model=List[QuestionVersionResponse])
async def get_question_versions(
    *,
    db: Session = Depends(get_db),
    question_id: int,
    skip: int = 0,
    limit: int = 100,
    current_user: User = Depends(get_current_user)
) -> Any:
    """
    获取题目版本列表
    """
    version_service = VersionService(db)
    versions = version_service.get_question_versions(question_id, skip, limit)
    return versions


@router.get("/knowledge-points/{kp_id}", response_model=List[KpVersionResponse])
async def get_kp_versions(
    *,
    db: Session = Depends(get_db),
    kp_id: int,
    skip: int = 0,
    limit: int = 100,
    current_user: User = Depends(get_current_user)
) -> Any:
    """
    获取知识点版本列表
    """
    version_service = VersionService(db)
    versions = version_service.get_kp_versions(kp_id, skip, limit)
    return versions


@router.get("/question/{version_id}", response_model=QuestionVersionResponse)
async def get_question_version_by_id(
    *,
    db: Session = Depends(get_db),
    version_id: int,
    current_user: User = Depends(get_current_user)
) -> Any:
    """
    根据ID获取题目版本
    """
    version_service = VersionService(db)
    version = version_service.get_version_by_id(version_id, "question")
    if not version:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="版本不存在"
        )
    return version


@router.get("/knowledge-point/{version_id}", response_model=KpVersionResponse)
async def get_kp_version_by_id(
    *,
    db: Session = Depends(get_db),
    version_id: int,
    current_user: User = Depends(get_current_user)
) -> Any:
    """
    根据ID获取知识点版本
    """
    version_service = VersionService(db)
    version = version_service.get_version_by_id(version_id, "kp")
    if not version:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="版本不存在"
        )
    return version


@router.post("/compare", response_model=VersionCompareResponse)
async def compare_versions(
    *,
    db: Session = Depends(get_db),
    request: VersionCompareRequest,
    version_type: str = Query("question", description="版本类型: question 或 kp"),
    current_user: User = Depends(get_current_user)
) -> Any:
    """
    比较两个版本
    """
    version_service = VersionService(db)
    try:
        comparison = version_service.compare_versions(
            request.source_version_id,
            request.target_version_id,
            version_type
        )
        return comparison
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.post("/rollback", response_model=VersionRollbackResponse)
async def rollback_version(
    *,
    db: Session = Depends(get_db),
    request: VersionRollbackRequest,
    version_type: str = Query("question", description="版本类型: question 或 kp"),
    current_user: User = Depends(get_current_user)
) -> Any:
    """
    回滚到指定版本
    """
    # 检查权限（只有管理员和专家可以回滚）
    if current_user.role not in ["admin", "expert"]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="权限不足，只有管理员和专家可以回滚版本"
        )
    
    version_service = VersionService(db)
    try:
        result = version_service.rollback_to_version(
            request.target_version_id,
            request.rollback_reason,
            current_user.id,
            version_type
        )
        return result
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.post("/publish", response_model=VersionPublishResponse)
async def publish_version(
    *,
    db: Session = Depends(get_db),
    request: VersionPublishRequest,
    version_type: str = Query("question", description="版本类型: question 或 kp"),
    current_user: User = Depends(get_current_user)
) -> Any:
    """
    发布版本
    """
    # 检查权限（只有管理员和专家可以发布）
    if current_user.role not in ["admin", "expert"]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="权限不足，只有管理员和专家可以发布版本"
        )
    
    version_service = VersionService(db)
    try:
        result = version_service.publish_version(
            request.version_id,
            request.publish_note,
            current_user.id,
            version_type
        )
        return result
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.get("/stats", response_model=VersionStatsResponse)
async def get_version_stats(
    *,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
) -> Any:
    """
    获取版本统计信息
    """
    version_service = VersionService(db)
    stats = version_service.get_version_stats()
    return stats
